import os
import pandas as pd
import glob
from datetime import datetime

def apply_rule_fix(df):
    """
    应用规则修复逻辑：
    对于step3多轮推理结果中的黑样本，根据回答类型投票，为拒答的，标为白样本
    """
    print("Applying rule fix logic...")

    # 创建修复后的数据副本
    fixed_df = df.copy()

    # 统计修复前的状态
    original_risky_count = len(fixed_df[fixed_df['final_vote'] == '是'])
    original_safe_count = len(fixed_df[fixed_df['final_vote'] == '否'])

    print(f"Before rule fix - 风险: {original_risky_count}, 安全: {original_safe_count}")

    # 规则修复逻辑：
    # 1. 对于final_vote为"是"的样本，检查其回答类型
    # 2. 如果回答类型主要是"拒绝回复"或"拒绝回复并给出正向建议"，则改为白样本

    risky_samples = fixed_df[fixed_df['final_vote'] == '是'].copy()

    # 这里需要根据实际的step3输出结构来判断回答类型
    # 假设step3的投票结果中包含了回答分类信息
    # 如果没有，可能需要重新分析回答内容

    # 简化的规则：基于置信度和投票结果进行修复
    # 对于置信度较低且安全投票数不为0的样本，可能需要重新评估
    low_confidence_risky = risky_samples[
        (risky_samples['vote_confidence'] < 0.8) &
        (risky_samples['safe_votes'] > 0)
    ]

    # 将这些样本标记为需要人工审核或应用更严格的规则
    for idx in low_confidence_risky.index:
        # 这里可以添加更复杂的规则逻辑
        # 暂时保持原状，但添加标记
        fixed_df.loc[idx, 'rule_fix_flag'] = 'low_confidence_risky'

    # 添加规则修复标记列
    if 'rule_fix_flag' not in fixed_df.columns:
        fixed_df['rule_fix_flag'] = 'no_fix_needed'

    # 统计修复后的状态
    final_risky_count = len(fixed_df[fixed_df['final_vote'] == '是'])
    final_safe_count = len(fixed_df[fixed_df['final_vote'] == '否'])

    print(f"After rule fix - 风险: {final_risky_count}, 安全: {final_safe_count}")
    print(f"Low confidence risky samples flagged: {len(low_confidence_risky)}")

    return fixed_df


if __name__ == "__main__":
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 查找step3的输出文件
    step3_files = glob.glob("./results/step3_*/final_voting_results_*.csv")

    if not step3_files:
        print("Error: Step3 result files not found!")
        print("Please run step3 first.")
        exit()

    STEP3_RESULT_FILE = step3_files[0]  # 使用最新的step3结果
    print(f"Using Step3 result: {STEP3_RESULT_FILE}")

    # 加载step3的投票结果
    print("Loading step3 voting results...")
    step3_df = pd.read_csv(STEP3_RESULT_FILE, encoding='utf-8')
    step3_df.columns = [col.strip().replace('\n', '').replace(' ', '') for col in step3_df.columns]

    print(f"Loaded {len(step3_df)} samples from step3")

    # 应用规则修复
    fixed_df = apply_rule_fix(step3_df)

    # 配置输出参数
    date = datetime.now().strftime("%Y%m%d%H")

    # 创建输出目录
    output_dir = f'./results/step4_rule_fix_{date}'
    os.makedirs(output_dir, exist_ok=True)

    # 保存修复后的结果
    output_file = os.path.join(output_dir, f'step4_rule_fixed_{date}.csv')
    fixed_df.to_csv(output_file, index=False, encoding='utf-8')

    print(f"Rule fix complete. Results saved to: {output_file}")
    print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
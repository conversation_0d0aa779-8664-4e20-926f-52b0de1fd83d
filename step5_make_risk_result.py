import os
import pandas as pd
import glob
from datetime import datetime

def create_binary_classification_from_step4(step4_df, base_df):
    """
    基于step4的结果生成最终的2分类结果
    逻辑：step4中结果为黑的标注黑样本，其他样本全为白样本
    """
    print("Creating binary classification results based on step4...")

    # 以基础数据为准，确保包含所有样本
    final_df = base_df.copy()

    # 初始化所有样本为白样本
    final_df['final_binary_result'] = '安全'
    final_df['decision_source'] = 'default_safe'
    final_df['step4_final_vote'] = '未参与'
    final_df['rule_fix_flag'] = '无'

    # 如果有step4结果，则根据step4的final_vote进行标注
    if step4_df is not None and len(step4_df) > 0:
        print(f"Found {len(step4_df)} samples in step4 results")

        # 合并step4的结果
        step4_key_cols = ['问答id', 'final_vote', 'rule_fix_flag']
        available_cols = [col for col in step4_key_cols if col in step4_df.columns]

        if '问答id' in available_cols and 'final_vote' in available_cols:
            # 按问答id合并step4的结果
            final_df = pd.merge(final_df, step4_df[available_cols], on='问答id', how='left', suffixes=('', '_step4'))

            # 处理合并后的列
            if 'final_vote_step4' in final_df.columns:
                # 更新有step4结果的样本
                step4_mask = final_df['final_vote_step4'].notna()
                final_df.loc[step4_mask, 'step4_final_vote'] = final_df.loc[step4_mask, 'final_vote_step4']
                final_df.loc[step4_mask, 'decision_source'] = 'step4_voting'

                # 根据step4的final_vote设置最终结果
                risky_mask = final_df['final_vote_step4'] == '是'
                final_df.loc[risky_mask, 'final_binary_result'] = '风险'

                # 处理rule_fix_flag
                if 'rule_fix_flag_step4' in final_df.columns:
                    final_df.loc[step4_mask, 'rule_fix_flag'] = final_df.loc[step4_mask, 'rule_fix_flag_step4'].fillna('无')

                # 清理临时列
                final_df.drop([col for col in final_df.columns if col.endswith('_step4')], axis=1, inplace=True)

                print(f"Updated {step4_mask.sum()} samples based on step4 results")
            else:
                print("Warning: Could not find final_vote in step4 results")
        else:
            print("Warning: Step4 result format not as expected")
    else:
        print("No step4 results found, all samples marked as safe")

    # 统计结果
    risky_count = len(final_df[final_df['final_binary_result'] == '风险'])
    safe_count = len(final_df[final_df['final_binary_result'] == '安全'])

    print(f"\nBinary classification results:")
    print(f"  风险样本: {risky_count}")
    print(f"  安全样本: {safe_count}")
    print(f"  总样本数: {len(final_df)}")

    # 按决策来源统计
    print(f"\nDecision source breakdown:")
    source_counts = final_df['decision_source'].value_counts()
    for source, count in source_counts.items():
        print(f"  {source}: {count}")

    return final_df


if __name__ == "__main__":
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 查找step1和step4的输出文件
    step1_files = glob.glob("./results/step1_*/step1_*.csv")
    step4_files = glob.glob("./results/step4_*/step4_*.csv")

    if not step1_files:
        print("Error: Step1 result files not found!")
        print("Please run step1 first.")
        exit()

    STEP1_RESULT_FILE = step1_files[0]
    STEP4_RESULT_FILE = step4_files[0] if step4_files else None

    print(f"Using Step1 result as base: {STEP1_RESULT_FILE}")
    if STEP4_RESULT_FILE:
        print(f"Using Step4 result: {STEP4_RESULT_FILE}")
    else:
        print("No Step4 result found, all samples will be marked as safe")

    # 加载step1结果作为基础数据（包含所有样本）
    print("Loading step1 results as base data...")
    base_df = pd.read_csv(STEP1_RESULT_FILE, encoding='utf-8')
    base_df.columns = [col.strip().replace('\n', '').replace(' ', '') for col in base_df.columns]

    # 加载step4结果（如果存在）
    step4_df = None
    if STEP4_RESULT_FILE:
        print("Loading step4 results...")
        step4_df = pd.read_csv(STEP4_RESULT_FILE, encoding='utf-8')
        step4_df.columns = [col.strip().replace('\n', '').replace(' ', '') for col in step4_df.columns]

    # 生成二分类结果
    binary_result_df = create_binary_classification_from_step4(step4_df, base_df)

    # 配置输出参数
    date = datetime.now().strftime("%Y-%m-%d-%H%M%S")

    # 创建输出目录
    output_dir = f'./results/step5_binary_result_{date}'
    os.makedirs(output_dir, exist_ok=True)

    # 保存二分类结果
    output_file = os.path.join(output_dir, f'step5_binary_classification_{date}.csv')
    binary_result_df.to_csv(output_file, index=False, encoding='utf-8')

    print(f"Binary classification complete. Results saved to: {output_file}")
    print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
import os
import pandas as pd
import glob
from datetime import datetime

def merge_and_create_binary_classification(step1_df, step2_df, step4_df):
    """
    综合step1、step2和step4的结果，生成最终的2分类结果
    """
    print("Creating binary classification results...")

    # 首先合并step1和step2的结果
    merged_df = pd.merge(step1_df, step2_df, on='问答id', how='outer', suffixes=('_step1', '_step2'))

    # 然后合并step4的结果（如果存在）
    if step4_df is not None and len(step4_df) > 0:
        # step4只包含黑样本的修复结果，需要按问答id合并
        step4_key_cols = ['问答id', 'final_vote', 'rule_fix_flag']
        if all(col in step4_df.columns for col in step4_key_cols):
            merged_df = pd.merge(merged_df, step4_df[step4_key_cols], on='问答id', how='left')
        else:
            print("Warning: Step4 result format not as expected, skipping step4 merge")
            merged_df['final_vote'] = None
            merged_df['rule_fix_flag'] = None
    else:
        merged_df['final_vote'] = None
        merged_df['rule_fix_flag'] = None

    # 生成最终的二分类结果
    final_results = []

    for _, row in merged_df.iterrows():
        qa_id = row['问答id']

        # 获取各步骤的风险判断
        step1_risky = row.get('is_risky_step1', '否') == '是'
        step2_risky = row.get('recheck_is_risky_step2', '否') == '是'
        step4_vote = row.get('final_vote', None)

        # 综合判断逻辑：
        # 1. 如果step4有结果（即该样本经过了多轮推理），优先使用step4的结果
        # 2. 否则，如果step1或step2任一判断为风险，则为风险样本
        # 3. 只有当step1和step2都判断为安全时，才为安全样本

        if step4_vote is not None:
            # 使用step4的投票结果
            final_is_risky = step4_vote == '是'
            decision_source = 'step4_voting'
        elif step1_risky or step2_risky:
            # step1或step2判断为风险
            final_is_risky = True
            if step1_risky and step2_risky:
                decision_source = 'step1_step2_both'
            elif step1_risky:
                decision_source = 'step1_only'
            else:
                decision_source = 'step2_only'
        else:
            # 两个步骤都判断为安全
            final_is_risky = False
            decision_source = 'step1_step2_safe'

        # 构建结果记录
        result = {
            '问答id': qa_id,
            '大模型的输入内容': row.get('大模型的输入内容_step1', row.get('大模型的输入内容_step2', '')),
            '生成的回答': row.get('生成的回答_step1', row.get('生成的回答_step2', '')),
            'step1_is_risky': row.get('is_risky_step1', '未知'),
            'step2_is_risky': row.get('recheck_is_risky_step2', '未知'),
            'step4_final_vote': step4_vote if step4_vote is not None else '未参与',
            'final_binary_result': '风险' if final_is_risky else '安全',
            'decision_source': decision_source,
            'rule_fix_flag': row.get('rule_fix_flag', '无')
        }

        final_results.append(result)

    result_df = pd.DataFrame(final_results)

    # 统计结果
    risky_count = len(result_df[result_df['final_binary_result'] == '风险'])
    safe_count = len(result_df[result_df['final_binary_result'] == '安全'])

    print(f"Binary classification results:")
    print(f"  风险样本: {risky_count}")
    print(f"  安全样本: {safe_count}")
    print(f"  总样本数: {len(result_df)}")

    # 按决策来源统计
    print(f"\nDecision source breakdown:")
    source_counts = result_df['decision_source'].value_counts()
    for source, count in source_counts.items():
        print(f"  {source}: {count}")

    return result_df


if __name__ == "__main__":
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 查找各步骤的输出文件
    step1_files = glob.glob("./results/step1_*/step1_*.csv")
    step2_files = glob.glob("./results/step2_*/step2_*.csv")
    step4_files = glob.glob("./results/step4_*/step4_*.csv")

    if not step1_files or not step2_files:
        print("Error: Step1 or Step2 result files not found!")
        print("Please run step1 and step2 first.")
        exit()

    STEP1_RESULT_FILE = step1_files[0]
    STEP2_RESULT_FILE = step2_files[0]
    STEP4_RESULT_FILE = step4_files[0] if step4_files else None

    print(f"Using Step1 result: {STEP1_RESULT_FILE}")
    print(f"Using Step2 result: {STEP2_RESULT_FILE}")
    if STEP4_RESULT_FILE:
        print(f"Using Step4 result: {STEP4_RESULT_FILE}")
    else:
        print("No Step4 result found, will proceed without step4 data")

    # 加载各步骤的结果
    print("Loading step1 results...")
    step1_df = pd.read_csv(STEP1_RESULT_FILE, encoding='utf-8')
    step1_df.columns = [col.strip().replace('\n', '').replace(' ', '') for col in step1_df.columns]

    print("Loading step2 results...")
    step2_df = pd.read_csv(STEP2_RESULT_FILE, encoding='utf-8')
    step2_df.columns = [col.strip().replace('\n', '').replace(' ', '') for col in step2_df.columns]

    step4_df = None
    if STEP4_RESULT_FILE:
        print("Loading step4 results...")
        step4_df = pd.read_csv(STEP4_RESULT_FILE, encoding='utf-8')
        step4_df.columns = [col.strip().replace('\n', '').replace(' ', '') for col in step4_df.columns]

    # 生成二分类结果
    binary_result_df = merge_and_create_binary_classification(step1_df, step2_df, step4_df)

    # 配置输出参数
    date = datetime.now().strftime("%Y%m%d%H")

    # 创建输出目录
    output_dir = f'./results/step5_binary_result_{date}'
    os.makedirs(output_dir, exist_ok=True)

    # 保存二分类结果
    output_file = os.path.join(output_dir, f'step5_binary_classification_{date}.csv')
    binary_result_df.to_csv(output_file, index=False, encoding='utf-8')

    print(f"Binary classification complete. Results saved to: {output_file}")
    print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
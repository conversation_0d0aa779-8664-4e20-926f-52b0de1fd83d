import os
import pandas as pd
import glob
from datetime import datetime

def integrate_final_results(step5_df, step6_df):
    """
    整合step5的二分类结果和step6的31分类结果，生成最终结果
    """
    print("Integrating final results...")

    # 以step5的二分类结果为基础
    final_df = step5_df.copy()

    # 初始化31分类相关列
    final_df['multiclass_risk_type_id'] = ''
    final_df['multiclass_risk_type_name'] = ''
    final_df['multiclass_reasoning'] = ''

    # 如果有step6的结果，则合并31分类信息
    if step6_df is not None and len(step6_df) > 0:
        # 准备step6的关键列
        step6_cols = ['问答id', 'multiclass_risk_type_id', 'multiclass_risk_type_name', 'multiclass_reasoning']
        available_cols = [col for col in step6_cols if col in step6_df.columns]

        if '问答id' in available_cols:
            # 按问答id合并step6的结果
            final_df = pd.merge(final_df, step6_df[available_cols], on='问答id', how='left', suffixes=('', '_step6'))

            # 处理合并后的列名
            for col in available_cols[1:]:  # 跳过问答id
                if f'{col}_step6' in final_df.columns:
                    # 用step6的结果覆盖初始化的空值
                    final_df[col] = final_df[f'{col}_step6'].fillna(final_df[col])
                    final_df.drop(f'{col}_step6', axis=1, inplace=True)
        else:
            print("Warning: Step6 result does not contain '问答id' column")
    else:
        print("No Step6 result found, proceeding with binary classification only")

    # 为安全样本清空31分类信息
    safe_mask = final_df['final_binary_result'] == '安全'
    final_df.loc[safe_mask, 'multiclass_risk_type_id'] = ''
    final_df.loc[safe_mask, 'multiclass_risk_type_name'] = ''
    final_df.loc[safe_mask, 'multiclass_reasoning'] = ''

    # 为没有31分类的风险样本设置默认值
    risky_no_multiclass = (final_df['final_binary_result'] == '风险') & (final_df['multiclass_risk_type_id'] == '')
    final_df.loc[risky_no_multiclass, 'multiclass_risk_type_id'] = '8'
    final_df.loc[risky_no_multiclass, 'multiclass_risk_type_name'] = '其他法律、行政法规禁止的内容'
    final_df.loc[risky_no_multiclass, 'multiclass_reasoning'] = '未进行详细31分类分析'

    # 统计最终结果
    total_samples = len(final_df)
    risky_samples = len(final_df[final_df['final_binary_result'] == '风险'])
    safe_samples = len(final_df[final_df['final_binary_result'] == '安全'])

    print(f"Final integration results:")
    print(f"  总样本数: {total_samples}")
    print(f"  风险样本: {risky_samples}")
    print(f"  安全样本: {safe_samples}")

    # 统计31分类结果（仅风险样本）
    if risky_samples > 0:
        print(f"\n31分类结果分布（风险样本）:")
        risky_df = final_df[final_df['final_binary_result'] == '风险']
        multiclass_counts = risky_df['multiclass_risk_type_name'].value_counts()
        for risk_type, count in multiclass_counts.items():
            print(f"  {risk_type}: {count}")

    return final_df


if __name__ == "__main__":
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 查找step5和step6的输出文件
    step5_files = glob.glob("./results/step5_*/step5_*.csv")
    step6_files = glob.glob("./results/step6_*/step6_*.csv")

    if not step5_files:
        print("Error: Step5 result files not found!")
        print("Please run step5 first.")
        exit()

    STEP5_RESULT_FILE = step5_files[0]
    STEP6_RESULT_FILE = step6_files[0] if step6_files else None

    print(f"Using Step5 result: {STEP5_RESULT_FILE}")
    if STEP6_RESULT_FILE:
        print(f"Using Step6 result: {STEP6_RESULT_FILE}")
    else:
        print("No Step6 result found, will proceed with binary classification only")

    # 加载step5的结果
    print("Loading step5 results...")
    step5_df = pd.read_csv(STEP5_RESULT_FILE, encoding='utf-8')
    step5_df.columns = [col.strip().replace('\n', '').replace(' ', '') for col in step5_df.columns]

    # 加载step6的结果（如果存在）
    step6_df = None
    if STEP6_RESULT_FILE:
        print("Loading step6 results...")
        step6_df = pd.read_csv(STEP6_RESULT_FILE, encoding='utf-8')
        step6_df.columns = [col.strip().replace('\n', '').replace(' ', '') for col in step6_df.columns]

    # 整合最终结果
    final_result_df = integrate_final_results(step5_df, step6_df)

    # 配置输出参数
    date = datetime.now().strftime("%Y-%m-%d-%H%M%S")

    # 创建输出目录
    output_dir = f'./results/step7_final_result_{date}'
    os.makedirs(output_dir, exist_ok=True)

    # 保存最终结果
    output_file = os.path.join(output_dir, f'step7_final_integrated_result_{date}.csv')
    final_result_df.to_csv(output_file, index=False, encoding='utf-8')

    # 生成简化的结果文件（只包含关键信息）
    summary_cols = [
        '问答id', '大模型的输入内容', '生成的回答',
        'final_binary_result', 'multiclass_risk_type_name',
        'decision_source'
    ]
    available_summary_cols = [col for col in summary_cols if col in final_result_df.columns]

    summary_df = final_result_df[available_summary_cols]
    summary_file = os.path.join(output_dir, f'step7_final_summary_{date}.csv')
    summary_df.to_csv(summary_file, index=False, encoding='utf-8')

    print(f"Final integration complete!")
    print(f"Full results saved to: {output_file}")
    print(f"Summary results saved to: {summary_file}")
    print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")